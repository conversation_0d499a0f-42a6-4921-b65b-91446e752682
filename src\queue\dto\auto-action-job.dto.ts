import { AutoMode } from '../../user/enums/auto-mode.enum';

export interface AutoActionJobData {
  userId: number;
  targetId: string;
  type: AutoMode;
}

export interface AutoActionJobOptions {
  repeat?: {
    pattern: string; // Cron pattern
  };
  delay?: number; // Delay in milliseconds
  removeOnComplete?: number;
  removeOnFail?: number;
  attempts?: number;
  backoff?: {
    type: string;
    delay: number;
  };
}
